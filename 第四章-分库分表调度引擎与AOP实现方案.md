# 第四章 - 分库分表调度引擎与AOP实现方案

## 4.1 整体架构：基于AOP的调度引擎设计

### 4.1.1 AOP调度架构概述

为将复杂的分库分表逻辑对业务代码实现"零侵入"，我们设计了一套解耦的、基于AOP的实现方案。其核心设计原则是：**AOP只做"调度"，不做"执行"**。

**架构设计理念：**
- **职责分离**：将AOP的功能拆分为多个职责单一、可独立测试的普通Java组件
- **调度与执行分离**：AOP切面专注于拦截和调度，具体的业务逻辑由专门的处理器执行
- **策略模式驱动**：通过策略模式实现不同场景下的灵活切换
- **配置化管理**：所有策略切换通过配置中心动态控制，无需重启应用

**整体架构层次：**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AOP切面调度    │───▶│   执行引擎        │───▶│  策略处理器      │
│ ShardingAspect  │    │ExecutionEngine   │    │ Strategy Handler│
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   上下文封装     │    │   事务边界管理    │    │   HintManager   │
│ QueryContext    │    │ Transaction Mgmt │    │  路由控制       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 4.1.2 核心组件关系设计

我们将整个调度体系拆分为三个核心层次：

**1. ShardingIndexAspect (AOP切面 - 调度层)**
- **职责**：唯一的`@Aspect`类，代码极简
- **功能**：拦截DAO方法，封装`QueryContext`上下文，转交给执行引擎
- **设计原则**：只做方法拦截和参数封装，不包含任何业务逻辑

**2. ShardingExecutionEngine (执行引擎 - 协调层)**
- **职责**：核心`@Service`，是**事务边界**的定义者
- **功能**：接收`QueryContext`，判断读/写操作，分发给相应处理器
- **事务管理**：统一管理"主表-索引表"复合操作的原子性

**3. Strategy Handler (策略处理器 - 执行层)**
- **WriteOperationHandler**：写操作处理器体系
- **ReadOperationHandler**：读操作处理器体系
- **装饰者模式**：通过装饰者模式实现策略的灵活组合

## 4.2 事务传播行为与原子性保障

### 4.2.1 事务边界设计

`ShardingExecutionEngine`的执行入口使用`@Transactional(propagation = Propagation.REQUIRED)`来确保"主表-索引表"复合操作的原子性。

**事务传播机制设计：**

```java
/**
 * 分片执行引擎 - 事务边界管理器
 * 负责统一管理分库分表操作的事务边界
 */
@Service
@Transactional(propagation = Propagation.REQUIRED)
public class ShardingExecutionEngine implements IShardingExecutionEngine {
    
    /**
     * 核心执行入口 - 事务边界定义点
     * @param context 查询上下文
     * @return 执行结果
     */
    public Object execute(QueryContext context) throws Throwable {
        // 事务边界内的统一调度逻辑
        OperationType operationType = operationDetector.detect(context);
        
        switch (operationType) {
            case WRITE:
                return writeHandler.handle(context);
            case READ:
                return readHandler.handle(context);
            default:
                throw new UnsupportedOperationException("不支持的操作类型: " + operationType);
        }
    }
}
```

**两种事务场景处理：**

1. **场景一：外部已存在大事务**
   - 当切面拦截的方法被`@Transactional`注解的Service方法调用时
   - 执行引擎会**加入（join）**到外部事务中
   - 主表和索引表的写入成为大事务的一部分，共同提交或回滚

2. **场景二：外部无事务（默认autocommit模式）**
   - 当切面拦截的方法被无`@Transactional`注解的方法调用时
   - 执行引擎会**创建全新事务**
   - 将"主表写入 + 索引表写入"包裹成新的原子单元

### 4.2.2 事务传播级别规范

为防止被调用的DAO方法使用错误的事务传播级别破坏设计的事务边界，我们制定严格的开发规范：

**禁用规则：**
- **严禁使用**`@Transactional(propagation = REQUIRES_NEW)`
- **DAO/Mapper层方法**不得定义独立的事务边界
- **所有事务控制**统一由执行引擎管理

**自动化架构守护：**
```java
/**
 * 事务传播级别检查器
 * 在CI/CD流程中自动检查代码规范
 */
@Component
public class TransactionPropagationValidator {
    
    /**
     * 检查DAO层方法的事务注解
     * 确保不使用REQUIRES_NEW传播级别
     */
    public void validateDaoTransactionAnnotations() {
        // 扫描所有@Repository和@Mapper注解的类
        // 检查方法上的@Transactional注解
        // 发现REQUIRES_NEW时抛出异常，阻止构建
    }
}
```

## 4.3 AOP中的分片处理引擎设计

### 4.3.1 处理引擎接口体系

我们通过接口抽象定义了整个处理引擎的核心契约：

```java
/**
 * 分片执行引擎核心接口
 * 定义了调度引擎的基本执行契约
 */
public interface IShardingExecutionEngine {
    /**
     * 执行分片操作
     * @param context 查询上下文，包含所有执行所需信息
     * @return 执行结果
     * @throws Throwable 执行过程中的任何异常
     */
    Object execute(QueryContext context) throws Throwable;
}

/**
 * 操作类型检测器接口
 * 负责从SQL和上下文中识别操作类型
 */
public interface IOperationTypeDetector {
    /**
     * 检测操作类型（读/写）
     * @param context 查询上下文
     * @return 操作类型枚举
     */
    OperationType detect(QueryContext context);
}

/**
 * 查询上下文构建器接口
 * 负责从AOP切点提取并封装查询信息
 */
public interface IQueryContextBuilder {
    /**
     * 从AOP切点构建查询上下文
     * @param joinPoint AOP切点对象
     * @return 封装完整的查询上下文
     */
    QueryContext build(ProceedingJoinPoint joinPoint);
}
```

### 4.3.2 执行引擎实现类关系

**主执行引擎实现：**
```java
/**
 * 分片执行引擎实现类
 * 作为整个调度体系的核心协调者
 */
@Service
public class ShardingExecutionEngine implements IShardingExecutionEngine {
    
    /** 操作类型检测器 - 识别读写操作 */
    @Autowired
    private IOperationTypeDetector operationDetector;
    
    /** 写操作处理器 - 处理所有写入操作 */
    @Autowired
    private IWriteOperationHandler writeHandler;
    
    /** 读操作处理器 - 处理所有查询操作 */
    @Autowired
    private IReadOperationHandler readHandler;
    
    /** 上下文管理器 - 管理分片上下文生命周期 */
    @Autowired
    private IShardingContextManager contextManager;
}
```

**操作类型检测器实现：**
```java
/**
 * SQL操作类型检测器
 * 通过SQL解析识别操作类型
 */
@Component
public class SqlOperationTypeDetector implements IOperationTypeDetector {
    
    /**
     * 基于SQL语句检测操作类型
     * @param context 包含SQL信息的查询上下文
     * @return 操作类型（READ/WRITE）
     */
    @Override
    public OperationType detect(QueryContext context) {
        String sql = context.getSql().trim().toUpperCase();
        
        // 写操作识别
        if (sql.startsWith("INSERT") || sql.startsWith("UPDATE") || 
            sql.startsWith("DELETE") || sql.startsWith("REPLACE")) {
            return OperationType.WRITE;
        }
        
        // 读操作识别
        if (sql.startsWith("SELECT")) {
            return OperationType.READ;
        }
        
        // 其他操作类型处理
        throw new UnsupportedOperationException("不支持的SQL操作类型: " + sql);
    }
}
```

### 4.3.3 上下文管理器设计

**查询上下文构建器：**
```java
/**
 * 查询上下文构建器实现
 * 负责从AOP切点提取完整的查询信息
 */
@Component
public class QueryContextBuilder implements IQueryContextBuilder {
    
    @Override
    public QueryContext build(ProceedingJoinPoint joinPoint) {
        QueryContext context = new QueryContext();
        
        // 提取方法信息
        context.setMethodName(joinPoint.getSignature().getName());
        context.setTargetClass(joinPoint.getTarget().getClass());
        
        // 提取SQL信息（通过MyBatis拦截器或注解）
        context.setSql(extractSqlFromJoinPoint(joinPoint));
        
        // 提取方法参数
        context.setMethodArgs(joinPoint.getArgs());
        
        // 提取分片键信息
        context.setShardingKeyValue(extractShardingKey(joinPoint));
        
        return context;
    }
    
    /**
     * 从切点提取SQL语句
     * 可通过MyBatis拦截器或方法注解获取
     */
    private String extractSqlFromJoinPoint(ProceedingJoinPoint joinPoint) {
        // 实现SQL提取逻辑
        // 可能需要与MyBatis拦截器配合
        return null;
    }
}
```

**分片上下文管理器：**
```java
/**
 * 分片上下文管理器
 * 负责管理ThreadLocal上下文和跨方法调用的上下文传递
 */
@Component
public class ShardingContextManager implements IShardingContextManager {
    
    /** ThreadLocal存储当前线程的分片上下文 */
    private static final ThreadLocal<ShardingContext> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前线程的分片上下文
     * @param context 分片上下文
     */
    public void setContext(ShardingContext context) {
        CONTEXT_HOLDER.set(context);
    }
    
    /**
     * 获取当前线程的分片上下文
     * @return 分片上下文，可能为null
     */
    public ShardingContext getContext() {
        return CONTEXT_HOLDER.get();
    }
    
    /**
     * 清理当前线程的分片上下文
     * 防止ThreadLocal内存泄漏
     */
    public void clearContext() {
        CONTEXT_HOLDER.remove();
    }
}
```

## 4.4 写策略接口与实现类体系

### 4.4.1 写操作处理器接口设计

写操作处理器体系通过统一接口和抽象基类，为不同的写入策略提供了灵活的扩展机制：

```java
/**
 * 写操作处理器统一接口
 * 定义了所有写操作策略的基本契约
 */
public interface IWriteOperationHandler {
    /**
     * 处理写操作
     * @param context 查询上下文，包含SQL、参数等信息
     * @return 写操作执行结果
     * @throws Throwable 写操作过程中的任何异常
     */
    Object handle(QueryContext context) throws Throwable;
}

/**
 * 写策略基础抽象类
 * 提供写操作的公共处理逻辑
 */
public abstract class AbstractWriteStrategy implements IWriteOperationHandler {
    
    /**
     * 信息补全策略 - 针对UPDATE操作
     * 对于不包含全量字段的UPDATE，采用"先更新主表，再SELECT最新数据"的策略
     * 获取完整实体信息用于写入索引表
     */
    protected Object completeEntityInfo(QueryContext context) {
        // 执行原始UPDATE操作
        Object updateResult = executeOriginalUpdate(context);
        
        // 根据ID重新SELECT最新数据
        Object completeEntity = selectCompleteEntity(context);
        
        return completeEntity;
    }
    
    /**
     * 写入前置处理
     * 包括参数验证、分片键提取等公共逻辑
     */
    protected void preWriteProcess(QueryContext context) {
        // 验证分片键是否存在
        validateShardingKey(context);

        // 提取并设置分片上下文
        setupShardingContext(context);
    }
}
```

### 4.4.2 标准写策略实现

标准写策略代表系统的最终稳态，只向新架构（分片表 + 索引表）执行写入操作：

```java
/**
 * 标准写策略 - 系统最终稳态
 * 只向新架构（分片表 + 索引表）执行标准的写入操作
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write",
                      havingValue = "N", matchIfMissing = true)
public class StandardWriteStrategy extends AbstractWriteStrategy {

    /** 分片表DAO - 处理主业务数据写入 */
    @Autowired
    private IShardingTableDao shardingTableDao;

    /** 索引表DAO - 处理索引数据写入 */
    @Autowired
    private IIndexTableDao indexTableDao;

    /**
     * 标准写入处理逻辑
     * 在同一事务内完成主表和索引表的写入
     */
    @Override
    public Object handle(QueryContext context) throws Throwable {
        // 执行写入前置处理
        preWriteProcess(context);

        try {
            // 1. 写入分片主表
            Object mainResult = writeToShardingTable(context);

            // 2. 写入索引表
            writeToIndexTable(context, mainResult);

            return mainResult;

        } catch (Exception e) {
            // 写入失败时的统一异常处理
            handleWriteException(context, e);
            throw e;
        }
    }

    /**
     * 写入分片主表
     * 利用ShardingSphere的自动路由能力
     */
    private Object writeToShardingTable(QueryContext context) {
        // ShardingSphere会根据分片键自动路由到具体分表
        return shardingTableDao.execute(context.getSql(), context.getMethodArgs());
    }

    /**
     * 写入索引表
     * 提取关键字段写入索引表，用于后续的非分片键查询
     */
    private void writeToIndexTable(QueryContext context, Object mainResult) {
        // 从主表写入结果中提取索引表所需字段
        IndexTableEntity indexEntity = extractIndexFields(context, mainResult);

        // 写入索引表（普通单表，无分片）
        indexTableDao.save(indexEntity);
    }
}
```

### 4.4.3 双写装饰策略实现

双写过渡策略通过装饰者模式包装标准写策略，在迁移期间实现新旧表的同步写入：

```java
/**
 * 双写过渡策略 - 装饰标准写策略
 * 迁移过渡期的临时策略，实现新旧表的双写同步
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write",
                      havingValue = "Y")
public class DualWriteTransitionalStrategy implements IWriteOperationHandler {

    /** 被装饰的标准写策略 */
    @Autowired
    private StandardWriteStrategy standardWriteStrategy;

    /** 旧表DAO - 处理旧架构的数据写入 */
    @Autowired
    private ILegacyTableDao legacyTableDao;

    /** 冲突记录DAO - 记录双写期间的数据冲突 */
    @Autowired
    private IConflictLogDao conflictLogDao;

    /** HintManager控制器 - 控制路由行为 */
    @Autowired
    private IHintManagerController hintController;

    /**
     * 双写处理逻辑
     * 遵循"先更新老表，再更新新表"的安全原则
     */
    @Override
    public Object handle(QueryContext context) throws Throwable {
        OperationType operationType = determineOperationType(context);

        switch (operationType) {
            case INSERT:
                return handleInsertOperation(context);
            case UPDATE:
                return handleUpdateOperation(context);
            case DELETE:
                return handleDeleteOperation(context);
            default:
                throw new UnsupportedOperationException("不支持的操作类型: " + operationType);
        }
    }

    /**
     * 处理INSERT操作 - 双写模式
     * INSERT操作会正常写入新、老两份表
     */
    private Object handleInsertOperation(QueryContext context) throws Throwable {
        // 1. 先写入老表（使用HintManager强制路由）
        Object legacyResult = writeToLegacyTable(context);

        // 2. 再写入新表（通过标准写策略）
        Object newResult = standardWriteStrategy.handle(context);

        return newResult;
    }

    /**
     * 处理UPDATE操作 - 特殊双写逻辑
     * 实现"存在即更新，不存在则记录冲突"的策略
     */
    private Object handleUpdateOperation(QueryContext context) throws Throwable {
        // 1. 先更新老表
        Object legacyResult = writeToLegacyTable(context);

        // 2. 尝试更新新表
        Object newResult = standardWriteStrategy.handle(context);

        // 3. 检查新表更新结果
        if (isUpdateMissed(newResult)) {
            // 新表中不存在该记录，记录到冲突日志表
            recordUpdateConflict(context);
        }

        return newResult;
    }

    /**
     * 写入旧表 - 使用HintManager强制路由
     */
    private Object writeToLegacyTable(QueryContext context) throws Throwable {
        try (AutoCloseable hintResource = hintController.routeToLegacyTable()) {
            // 强制路由到旧表，绕过所有分片规则
            return legacyTableDao.execute(context.getSql(), context.getMethodArgs());
        }
    }

    /**
     * 记录UPDATE冲突
     * 将未命中的UPDATE记录到冲突日志表，由后续脚本处理
     */
    private void recordUpdateConflict(QueryContext context) {
        ConflictLogEntity conflict = new ConflictLogEntity();
        conflict.setEntityId(extractEntityId(context));
        conflict.setConflictType("UPDATE_MISS");
        conflict.setConflictTime(new Date());
        conflict.setContextInfo(context.toString());

        conflictLogDao.save(conflict);
    }
}
```

### 4.4.4 写策略的HintManager集成

HintManager控制器封装了路由控制的复杂性，提供简洁的API：

```java
/**
 * HintManager路由控制接口
 * 封装ShardingSphere HintManager的复杂操作
 */
public interface IHintManagerController {
    /**
     * 路由到旧表
     * @return 自动关闭资源，确保ThreadLocal清理
     */
    AutoCloseable routeToLegacyTable();

    /**
     * 路由到新表
     * @return 自动关闭资源，确保ThreadLocal清理
     */
    AutoCloseable routeToNewTable();

    /**
     * 设置数据库分片值
     * @param shardingValue 分片键值
     * @return 自动关闭资源
     */
    AutoCloseable setDatabaseShardingValue(String shardingValue);
}

/**
 * 写策略HintManager控制器实现
 * 基于try-with-resources模式的安全路由控制
 */
@Component
public class WriteStrategyHintController implements IHintManagerController {

    @Override
    public AutoCloseable routeToLegacyTable() {
        HintManager hintManager = HintManager.getInstance();

        // 设置强制路由到物理表，绕过分片规则
        hintManager.setWriteRouteOnly();

        // 返回自动关闭资源，确保HintManager被正确清理
        return () -> {
            try {
                hintManager.close();
            } catch (Exception e) {
                log.warn("关闭HintManager时发生异常", e);
            }
        };
    }

    @Override
    public AutoCloseable routeToNewTable() {
        HintManager hintManager = HintManager.getInstance();

        // 新表路由通过ShardingSphere的正常分片规则
        // 这里主要是为了API一致性，实际可能不需要特殊设置

        return () -> {
            try {
                hintManager.close();
            } catch (Exception e) {
                log.warn("关闭HintManager时发生异常", e);
            }
        };
    }

    @Override
    public AutoCloseable setDatabaseShardingValue(String shardingValue) {
        HintManager hintManager = HintManager.getInstance();

        // 设置数据库分片值，用于精确控制分库路由
        hintManager.addDatabaseShardingValue("t_order", shardingValue);

        return () -> {
            try {
                hintManager.close();
            } catch (Exception e) {
                log.warn("关闭HintManager时发生异常", e);
            }
        };
    }
}
```

## 4.5 读策略接口与实现类体系

### 4.5.1 读操作处理器接口设计

读操作处理器体系专注于智能路由所有读请求，通过策略模式实现不同场景下的灵活切换：

```java
/**
 * 读操作处理器统一接口
 * 定义了所有读操作策略的基本契约
 */
public interface IReadOperationHandler {
    /**
     * 处理读操作
     * @param context 查询上下文，包含SQL、参数等信息
     * @return 查询结果
     * @throws Throwable 查询过程中的任何异常
     */
    Object handle(QueryContext context) throws Throwable;
}

/**
 * 读策略基础抽象类
 * 提供读操作的公共处理逻辑
 */
public abstract class AbstractReadStrategy implements IReadOperationHandler {

    /** 分片键检测器 */
    @Autowired
    protected IShardingKeyDetector shardingKeyDetector;

    /** 查询路由决策器 */
    @Autowired
    protected IQueryRoutingDecisionMaker routingDecisionMaker;

    /**
     * 分片键检测与路由决策
     * 判断查询是否包含分片键，决定查询路由策略
     */
    protected RoutingDecision makeRoutingDecision(QueryContext context) {
        // 检测是否包含分片键
        boolean hasShardingKey = shardingKeyDetector.hasShardingKey(context);

        if (hasShardingKey) {
            // 包含分片键：直接路由到分表
            return RoutingDecision.DIRECT_SHARDING;
        } else {
            // 不包含分片键：通过决策器确定路由策略
            return routingDecisionMaker.makeDecision(context);
        }
    }

    /**
     * 执行两阶段查询
     * 先查索引表获取分片键，再查分表获取完整数据
     */
    protected Object executeTwoPhaseQuery(QueryContext context) {
        // 第一阶段：查询索引表获取ID列表
        List<String> idList = queryIndexTableForIds(context);

        if (idList.isEmpty()) {
            return Collections.emptyList();
        }

        // 第二阶段：根据ID精确查询分表
        return queryShardingTableByIds(context, idList);
    }
}
```

### 4.5.2 分片读策略实现

分片读策略代表系统的最终稳态，提供性能最优的查询能力：

```java
/**
 * 分片读策略 - 系统最终稳态，性能最优
 * 高效查询索引表和分片表，不包含任何灰度逻辑
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.read.path.mode",
                      havingValue = "sharding", matchIfMissing = true)
public class ShardingReadStrategy extends AbstractReadStrategy {

    /** 索引表DAO */
    @Autowired
    private IIndexTableDao indexTableDao;

    /** 分片表DAO */
    @Autowired
    private IShardingTableDao shardingTableDao;

    /** 查询策略管理器 */
    @Autowired
    private IQueryStrategyManager queryStrategyManager;

    /**
     * 分片读处理逻辑
     * 根据查询特征选择最优的执行策略
     */
    @Override
    public Object handle(QueryContext context) throws Throwable {
        // 制定路由决策
        RoutingDecision decision = makeRoutingDecision(context);

        switch (decision) {
            case DIRECT_SHARDING:
                // 包含分片键：直接查询分表
                return executeDirectShardingQuery(context);

            case INDEX_THEN_SCAN:
                // 不包含分片键：两阶段查询
                return executeTwoPhaseQuery(context);

            case LIMITED_SCAN:
                // 受限全表扫描：有限制的查询
                return executeLimitedScanQuery(context);

            case FORBIDDEN:
                // 禁止的查询类型
                throw new UnsupportedQueryException("不支持的查询类型: " + context.getSql());

            default:
                throw new IllegalStateException("未知的路由决策: " + decision);
        }
    }

    /**
     * 执行直接分片查询
     * 利用ShardingSphere的自动路由能力
     */
    private Object executeDirectShardingQuery(QueryContext context) {
        // ShardingSphere会根据分片键自动路由到具体分表
        return shardingTableDao.execute(context.getSql(), context.getMethodArgs());
    }

    /**
     * 执行受限扫描查询
     * 对不包含分片键的查询进行限制和监控
     */
    private Object executeLimitedScanQuery(QueryContext context) {
        // 应用查询限制（如LIMIT子句）
        QueryContext limitedContext = queryStrategyManager.applyQueryLimits(context);

        // 记录受限查询的监控信息
        recordLimitedScanMetrics(limitedContext);

        // 执行受限查询
        return shardingTableDao.execute(limitedContext.getSql(), limitedContext.getMethodArgs());
    }

    /**
     * 记录受限扫描的监控指标
     */
    private void recordLimitedScanMetrics(QueryContext context) {
        // 记录到监控系统，用于性能分析和优化
        log.warn("执行受限扫描查询: SQL={}, Method={}",
                context.getSql(), context.getMethodName());
    }
}
```

### 4.5.3 灰度读装饰策略实现

灰度读过渡策略通过装饰者模式包装分片读策略，实现灰度发布期间的流量控制：

```java
/**
 * 灰度读过渡策略 - 装饰分片读策略
 * 覆盖整个过渡期的临时策略，实现灰度流量控制
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.read.path.mode",
                      havingValue = "grayscale_migration")
public class GrayscaleReadTransitionalStrategy implements IReadOperationHandler {

    /** 被装饰的分片读策略 */
    @Autowired
    private ShardingReadStrategy shardingReadStrategy;

    /** 旧表DAO */
    @Autowired
    private ILegacyTableDao legacyTableDao;

    /** 灰度路由决策器 */
    @Autowired
    private IGrayscaleRoutingDecisionMaker grayscaleDecisionMaker;

    /** HintManager控制器 */
    @Autowired
    private IHintManagerController hintController;

    /** 灰度配置管理器 */
    @Autowired
    private IGrayscaleConfigManager configManager;

    /**
     * 灰度读处理逻辑
     * 根据灰度配置决定读取新表还是旧表
     */
    @Override
    public Object handle(QueryContext context) throws Throwable {
        // 获取当前灰度百分比配置
        int grayscalePercentage = configManager.getGrayscalePercentage();

        // 根据灰度百分比决定读取策略
        GrayscaleDecision decision = grayscaleDecisionMaker.makeDecision(context, grayscalePercentage);

        switch (decision) {
            case READ_NEW_TABLE:
                // 读取新表：使用分片读策略
                return readFromNewTable(context);

            case READ_OLD_TABLE:
                // 读取旧表：使用HintManager强制路由
                return readFromOldTable(context);

            case READ_BOTH_AND_COMPARE:
                // 双读对比：用于数据一致性验证
                return readBothAndCompare(context);

            default:
                throw new IllegalStateException("未知的灰度决策: " + decision);
        }
    }

    /**
     * 从新表读取数据
     * 委托给分片读策略处理
     */
    private Object readFromNewTable(QueryContext context) throws Throwable {
        // 记录新表读取指标
        recordNewTableReadMetrics(context);

        // 委托给分片读策略
        return shardingReadStrategy.handle(context);
    }

    /**
     * 从旧表读取数据
     * 使用HintManager强制路由到旧表
     */
    private Object readFromOldTable(QueryContext context) throws Throwable {
        // 记录旧表读取指标
        recordOldTableReadMetrics(context);

        try (AutoCloseable hintResource = hintController.routeToLegacyTable()) {
            // 强制路由到旧表
            return legacyTableDao.execute(context.getSql(), context.getMethodArgs());
        }
    }

    /**
     * 双读对比模式
     * 同时读取新旧表数据进行一致性对比
     */
    private Object readBothAndCompare(QueryContext context) throws Throwable {
        // 读取新表数据
        Object newResult = readFromNewTable(context);

        // 读取旧表数据
        Object oldResult = readFromOldTable(context);

        // 异步对比数据一致性
        compareDataConsistencyAsync(context, newResult, oldResult);

        // 返回新表数据（以新表为准）
        return newResult;
    }

    /**
     * 异步对比数据一致性
     * 不影响主流程性能，用于监控数据迁移质量
     */
    private void compareDataConsistencyAsync(QueryContext context, Object newResult, Object oldResult) {
        // 提交到异步线程池进行数据对比
        CompletableFuture.runAsync(() -> {
            try {
                boolean isConsistent = dataConsistencyChecker.compare(newResult, oldResult);
                if (!isConsistent) {
                    // 记录数据不一致情况
                    recordDataInconsistency(context, newResult, oldResult);
                }
            } catch (Exception e) {
                log.error("数据一致性对比失败", e);
            }
        });
    }
}
```

### 4.5.4 读策略的查询路由机制

查询路由决策器负责根据查询特征和系统配置做出智能的路由决策：

```java
/**
 * 查询路由决策器接口
 * 负责根据查询上下文做出路由决策
 */
public interface IQueryRoutingDecisionMaker {
    /**
     * 制定路由决策
     * @param context 查询上下文
     * @return 路由决策结果
     */
    RoutingDecision makeDecision(QueryContext context);
}

/**
 * 灰度路由决策器接口
 * 专门处理灰度期间的路由决策
 */
public interface IGrayscaleRoutingDecisionMaker {
    /**
     * 制定灰度路由决策
     * @param context 查询上下文
     * @param grayscalePercentage 灰度百分比
     * @return 灰度决策结果
     */
    GrayscaleDecision makeDecision(QueryContext context, int grayscalePercentage);
}

/**
 * 灰度路由决策实现
 * 基于配置百分比和用户维度的一致性路由
 */
@Component
public class GrayscaleRoutingDecisionMaker implements IGrayscaleRoutingDecisionMaker {

    /** 用户路由一致性管理器 */
    @Autowired
    private IUserRoutingConsistencyManager consistencyManager;

    @Override
    public GrayscaleDecision makeDecision(QueryContext context, int grayscalePercentage) {
        // 特殊百分比处理
        if (grayscalePercentage == 0) {
            // 安全上线/回滚：只读旧表
            return GrayscaleDecision.READ_OLD_TABLE;
        }

        if (grayscalePercentage == 100) {
            // 观察期：全量读新表
            return GrayscaleDecision.READ_NEW_TABLE;
        }

        // 灰度期间：基于用户维度的一致性路由
        String userId = extractUserId(context);
        if (userId != null) {
            // 确保同一用户的所有请求路由到同一数据源
            return consistencyManager.getConsistentRouting(userId, grayscalePercentage);
        }

        // 无用户信息：基于随机百分比
        return makeRandomDecision(grayscalePercentage);
    }

    /**
     * 基于随机百分比做决策
     */
    private GrayscaleDecision makeRandomDecision(int grayscalePercentage) {
        int random = ThreadLocalRandom.current().nextInt(100);

        if (random < grayscalePercentage) {
            return GrayscaleDecision.READ_NEW_TABLE;
        } else {
            return GrayscaleDecision.READ_OLD_TABLE;
        }
    }
}
```

## 4.6 HintManager手动路由机制

### 4.6.1 HintManager封装设计

为了简化HintManager的使用并确保资源的正确释放，我们设计了统一的封装接口：

```java
/**
 * HintManager操作封装接口
 * 提供类型安全和资源安全的HintManager操作
 */
public interface IHintManagerOperations {
    /**
     * 设置数据库分片值
     * @param logicTable 逻辑表名
     * @param shardingValue 分片键值
     * @return 自动关闭资源
     */
    AutoCloseable setDatabaseShardingValue(String logicTable, String shardingValue);

    /**
     * 设置表分片值
     * @param logicTable 逻辑表名
     * @param shardingValue 分片键值
     * @return 自动关闭资源
     */
    AutoCloseable setTableShardingValue(String logicTable, String shardingValue);

    /**
     * 设置只写路由
     * @return 自动关闭资源
     */
    AutoCloseable setWriteRouteOnly();

    /**
     * 设置只读路由
     * @return 自动关闭资源
     */
    AutoCloseable setReadRouteOnly();
}

/**
 * HintManager安全操作实现
 * 基于try-with-resources模式的安全封装
 */
@Component
public class SafeHintManagerOperations implements IHintManagerOperations {

    @Override
    public AutoCloseable setDatabaseShardingValue(String logicTable, String shardingValue) {
        HintManager hintManager = HintManager.getInstance();
        hintManager.addDatabaseShardingValue(logicTable, shardingValue);

        return createAutoCloseableHint(hintManager);
    }

    @Override
    public AutoCloseable setTableShardingValue(String logicTable, String shardingValue) {
        HintManager hintManager = HintManager.getInstance();
        hintManager.addTableShardingValue(logicTable, shardingValue);

        return createAutoCloseableHint(hintManager);
    }

    @Override
    public AutoCloseable setWriteRouteOnly() {
        HintManager hintManager = HintManager.getInstance();
        hintManager.setWriteRouteOnly();

        return createAutoCloseableHint(hintManager);
    }

    @Override
    public AutoCloseable setReadRouteOnly() {
        HintManager hintManager = HintManager.getInstance();
        hintManager.setReadRouteOnly();

        return createAutoCloseableHint(hintManager);
    }

    /**
     * 创建自动关闭的HintManager资源
     * 确保ThreadLocal被正确清理，防止内存泄漏
     */
    private AutoCloseable createAutoCloseableHint(HintManager hintManager) {
        return () -> {
            try {
                hintManager.close();
            } catch (Exception e) {
                log.warn("关闭HintManager时发生异常", e);
                // 不重新抛出异常，避免影响业务流程
            }
        };
    }
}
```

### 4.6.2 手动路由策略集成

手动路由策略专门处理需要精确控制路由的特殊场景：

```java
/**
 * 手动路由策略接口
 * 处理需要精确路由控制的特殊场景
 */
public interface IManualRoutingStrategy {
    /**
     * 应用手动路由规则
     * @param context 查询上下文
     * @return 路由控制资源，需要在finally中关闭
     */
    AutoCloseable applyRouting(QueryContext context);
}

/**
 * 迁移期手动路由实现
 * 专门处理数据迁移期间的精确路由需求
 */
@Component
public class MigrationManualRoutingStrategy implements IManualRoutingStrategy {

    /** HintManager操作封装 */
    @Autowired
    private IHintManagerOperations hintOperations;

    /** 迁移配置管理器 */
    @Autowired
    private IMigrationConfigManager migrationConfig;

    @Override
    public AutoCloseable applyRouting(QueryContext context) {
        // 根据迁移阶段决定路由策略
        MigrationPhase currentPhase = migrationConfig.getCurrentPhase();

        switch (currentPhase) {
            case DUAL_WRITE_PHASE:
                return applyDualWriteRouting(context);

            case GRAYSCALE_READ_PHASE:
                return applyGrayscaleReadRouting(context);

            case MIGRATION_COMPLETE:
                return applyStandardRouting(context);

            default:
                throw new IllegalStateException("未知的迁移阶段: " + currentPhase);
        }
    }

    /**
     * 应用双写期路由
     * 根据操作类型选择不同的路由策略
     */
    private AutoCloseable applyDualWriteRouting(QueryContext context) {
        if (context.isWriteOperation()) {
            // 写操作：需要特殊的双写处理，不使用HintManager
            return () -> {}; // 空的AutoCloseable
        } else {
            // 读操作：默认读旧表
            return hintOperations.setReadRouteOnly();
        }
    }

    /**
     * 应用灰度读期路由
     * 根据灰度配置决定读取源
     */
    private AutoCloseable applyGrayscaleReadRouting(QueryContext context) {
        int grayscalePercentage = migrationConfig.getGrayscalePercentage();

        if (shouldRouteToNewTable(context, grayscalePercentage)) {
            // 路由到新表：使用正常的分片规则
            return () -> {}; // 空的AutoCloseable
        } else {
            // 路由到旧表：强制路由
            return hintOperations.setReadRouteOnly();
        }
    }

    /**
     * 应用标准路由
     * 迁移完成后的正常路由
     */
    private AutoCloseable applyStandardRouting(QueryContext context) {
        // 迁移完成后，所有操作都使用标准的分片规则
        return () -> {}; // 空的AutoCloseable
    }
}
```

## 4.7 调度策略的配置化管理

### 4.7.1 策略配置接口

配置化管理是整个调度体系灵活性的基础，支持动态切换不同的策略：

```java
/**
 * 调度配置管理接口
 * 统一管理所有分片调度相关的配置
 */
public interface IShardingStrategyConfig {
    /**
     * 是否启用双写模式
     * @return true表示启用双写，false表示标准写入
     */
    boolean isDualWriteEnabled();

    /**
     * 获取读路径模式
     * @return 读路径模式（sharding/grayscale_migration）
     */
    String getReadPathMode();

    /**
     * 获取灰度百分比
     * @return 灰度百分比（0-100）
     */
    int getGrayscalePercentage();

    /**
     * 获取查询限制配置
     * @return 查询限制配置对象
     */
    QueryLimitConfig getQueryLimitConfig();
}

/**
 * 动态配置实现
 * 集成配置中心，支持实时配置更新
 */
@Component
public class DynamicShardingStrategyConfig implements IShardingStrategyConfig {

    /** 双写开关配置键 */
    private static final String DUAL_WRITE_KEY = "sharding-architecture.dual-write";

    /** 读路径模式配置键 */
    private static final String READ_PATH_MODE_KEY = "sharding-architecture.read.path.mode";

    /** 灰度百分比配置键 */
    private static final String GRAYSCALE_PERCENTAGE_KEY = "sharding-architecture.grayscale.percentage";

    /** 配置中心客户端 */
    @Autowired
    private IConfigCenterClient configCenterClient;

    /** 配置变更监听器 */
    @Autowired
    private IConfigChangeListener configChangeListener;

    @Override
    public boolean isDualWriteEnabled() {
        String value = configCenterClient.getConfig(DUAL_WRITE_KEY, "N");
        return "Y".equalsIgnoreCase(value);
    }

    @Override
    public String getReadPathMode() {
        return configCenterClient.getConfig(READ_PATH_MODE_KEY, "sharding");
    }

    @Override
    public int getGrayscalePercentage() {
        String value = configCenterClient.getConfig(GRAYSCALE_PERCENTAGE_KEY, "0");
        try {
            int percentage = Integer.parseInt(value);
            return Math.max(0, Math.min(100, percentage)); // 确保在0-100范围内
        } catch (NumberFormatException e) {
            log.warn("灰度百分比配置格式错误: {}, 使用默认值0", value);
            return 0;
        }
    }

    /**
     * 初始化配置监听
     * 当配置发生变化时，触发相应的处理逻辑
     */
    @PostConstruct
    public void initConfigListener() {
        // 监听双写配置变化
        configChangeListener.addListener(DUAL_WRITE_KEY, this::onDualWriteConfigChanged);

        // 监听读路径配置变化
        configChangeListener.addListener(READ_PATH_MODE_KEY, this::onReadPathModeChanged);

        // 监听灰度百分比变化
        configChangeListener.addListener(GRAYSCALE_PERCENTAGE_KEY, this::onGrayscalePercentageChanged);
    }

    /**
     * 双写配置变化处理
     */
    private void onDualWriteConfigChanged(String oldValue, String newValue) {
        log.info("双写配置发生变化: {} -> {}", oldValue, newValue);
        // 可以在这里触发相关的处理逻辑，如预热新的策略实例等
    }
}
```

### 4.7.2 策略工厂模式

策略工厂负责根据配置创建和管理不同的策略实例：

```java
/**
 * 策略工厂接口
 * 负责根据配置创建相应的策略实例
 */
public interface IStrategyFactory {
    /**
     * 创建写操作策略
     * @return 当前配置下的写操作处理器
     */
    IWriteOperationHandler createWriteStrategy();

    /**
     * 创建读操作策略
     * @return 当前配置下的读操作处理器
     */
    IReadOperationHandler createReadStrategy();
}

/**
 * 条件化策略工厂实现
 * 基于Spring的条件化配置和依赖注入
 */
@Component
public class ConditionalStrategyFactory implements IStrategyFactory {

    /** 配置管理器 */
    @Autowired
    private IShardingStrategyConfig strategyConfig;

    /** Spring应用上下文 */
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public IWriteOperationHandler createWriteStrategy() {
        if (strategyConfig.isDualWriteEnabled()) {
            // 双写模式：返回双写过渡策略
            return applicationContext.getBean(DualWriteTransitionalStrategy.class);
        } else {
            // 标准模式：返回标准写策略
            return applicationContext.getBean(StandardWriteStrategy.class);
        }
    }

    @Override
    public IReadOperationHandler createReadStrategy() {
        String readPathMode = strategyConfig.getReadPathMode();

        switch (readPathMode) {
            case "grayscale_migration":
                // 灰度迁移模式：返回灰度读策略
                return applicationContext.getBean(GrayscaleReadTransitionalStrategy.class);

            case "sharding":
            default:
                // 分片模式：返回标准分片读策略
                return applicationContext.getBean(ShardingReadStrategy.class);
        }
    }
}
```

## 4.8 UML类图设计

为了更清晰地展示分库分表调度引擎的设计结构，我们提供以下UML类图：

### 4.8.1 整体架构类图

```mermaid
classDiagram
    %% AOP切面层
    class ShardingIndexAspect {
        <<@Aspect @Component>>
        -executionEngine: IShardingExecutionEngine
        -contextBuilder: IQueryContextBuilder
        +around(ProceedingJoinPoint joinPoint) Object
        -buildQueryContext(ProceedingJoinPoint) QueryContext
    }

    %% 执行引擎层
    class IShardingExecutionEngine {
        <<interface>>
        +execute(QueryContext context) Object
    }

    class ShardingExecutionEngine {
        <<@Service @Transactional>>
        -operationDetector: IOperationTypeDetector
        -writeHandler: IWriteOperationHandler
        -readHandler: IReadOperationHandler
        -contextManager: IShardingContextManager
        +execute(QueryContext context) Object
    }

    %% 上下文管理
    class QueryContext {
        -methodName: String
        -sql: String
        -methodArgs: Object[]
        -shardingKeyValue: String
        +getSql() String
        +getMethodArgs() Object[]
        +hasShardingKey() boolean
    }

    class IQueryContextBuilder {
        <<interface>>
        +build(ProceedingJoinPoint) QueryContext
    }

    class QueryContextBuilder {
        <<@Component>>
        +build(ProceedingJoinPoint) QueryContext
        -extractSqlFromJoinPoint(ProceedingJoinPoint) String
    }

    %% 操作类型检测
    class IOperationTypeDetector {
        <<interface>>
        +detect(QueryContext) OperationType
    }

    class SqlOperationTypeDetector {
        <<@Component>>
        +detect(QueryContext) OperationType
    }

    %% 关系定义
    ShardingIndexAspect --> IShardingExecutionEngine : uses
    ShardingIndexAspect --> IQueryContextBuilder : uses
    ShardingExecutionEngine ..|> IShardingExecutionEngine
    ShardingExecutionEngine --> IOperationTypeDetector : uses
    QueryContextBuilder ..|> IQueryContextBuilder
    SqlOperationTypeDetector ..|> IOperationTypeDetector
    QueryContextBuilder --> QueryContext : creates
```

### 4.8.2 写策略体系类图

```mermaid
classDiagram
    %% 写策略接口和实现
    class IWriteOperationHandler {
        <<interface>>
        +handle(QueryContext context) Object
    }

    class AbstractWriteStrategy {
        <<abstract>>
        +completeEntityInfo(QueryContext) Object
        +preWriteProcess(QueryContext) void
        #validateShardingKey(QueryContext) void
    }

    class StandardWriteStrategy {
        <<@Component>>
        <<@ConditionalOnProperty(dual-write=N)>>
        -shardingTableDao: IShardingTableDao
        -indexTableDao: IIndexTableDao
        +handle(QueryContext context) Object
        -writeToShardingTable(QueryContext) Object
        -writeToIndexTable(QueryContext, Object) void
    }

    class DualWriteTransitionalStrategy {
        <<@Component>>
        <<@ConditionalOnProperty(dual-write=Y)>>
        -standardWriteStrategy: StandardWriteStrategy
        -legacyTableDao: ILegacyTableDao
        -conflictLogDao: IConflictLogDao
        -hintController: IHintManagerController
        +handle(QueryContext context) Object
        -handleInsertOperation(QueryContext) Object
        -handleUpdateOperation(QueryContext) Object
        -writeToLegacyTable(QueryContext) Object
        -recordUpdateConflict(QueryContext) void
    }

    %% HintManager控制器
    class IHintManagerController {
        <<interface>>
        +routeToLegacyTable() AutoCloseable
        +routeToNewTable() AutoCloseable
        +setDatabaseShardingValue(String) AutoCloseable
    }

    class WriteStrategyHintController {
        <<@Component>>
        +routeToLegacyTable() AutoCloseable
        +routeToNewTable() AutoCloseable
        +setDatabaseShardingValue(String) AutoCloseable
    }

    %% 关系定义
    AbstractWriteStrategy ..|> IWriteOperationHandler
    StandardWriteStrategy --|> AbstractWriteStrategy
    DualWriteTransitionalStrategy ..|> IWriteOperationHandler
    DualWriteTransitionalStrategy --> StandardWriteStrategy : decorates
    DualWriteTransitionalStrategy --> IHintManagerController : uses
    WriteStrategyHintController ..|> IHintManagerController
```

### 4.8.3 读策略体系类图

```mermaid
classDiagram
    %% 读策略接口和实现
    class IReadOperationHandler {
        <<interface>>
        +handle(QueryContext context) Object
    }

    class AbstractReadStrategy {
        <<abstract>>
        #shardingKeyDetector: IShardingKeyDetector
        #routingDecisionMaker: IQueryRoutingDecisionMaker
        +makeRoutingDecision(QueryContext) RoutingDecision
        +executeTwoPhaseQuery(QueryContext) Object
    }

    class ShardingReadStrategy {
        <<@Component>>
        <<@ConditionalOnProperty(read.path.mode=sharding)>>
        -indexTableDao: IIndexTableDao
        -shardingTableDao: IShardingTableDao
        -queryStrategyManager: IQueryStrategyManager
        +handle(QueryContext context) Object
        -executeDirectShardingQuery(QueryContext) Object
        -executeLimitedScanQuery(QueryContext) Object
    }

    class GrayscaleReadTransitionalStrategy {
        <<@Component>>
        <<@ConditionalOnProperty(read.path.mode=grayscale_migration)>>
        -shardingReadStrategy: ShardingReadStrategy
        -legacyTableDao: ILegacyTableDao
        -grayscaleDecisionMaker: IGrayscaleRoutingDecisionMaker
        -hintController: IHintManagerController
        -configManager: IGrayscaleConfigManager
        +handle(QueryContext context) Object
        -readFromNewTable(QueryContext) Object
        -readFromOldTable(QueryContext) Object
        -readBothAndCompare(QueryContext) Object
    }

    %% 路由决策相关
    class IGrayscaleRoutingDecisionMaker {
        <<interface>>
        +makeDecision(QueryContext, int) GrayscaleDecision
    }

    class GrayscaleRoutingDecisionMaker {
        <<@Component>>
        -consistencyManager: IUserRoutingConsistencyManager
        +makeDecision(QueryContext, int) GrayscaleDecision
        -makeRandomDecision(int) GrayscaleDecision
    }

    %% 枚举类型
    class RoutingDecision {
        <<enumeration>>
        DIRECT_SHARDING
        INDEX_THEN_SCAN
        LIMITED_SCAN
        FORBIDDEN
    }

    class GrayscaleDecision {
        <<enumeration>>
        READ_NEW_TABLE
        READ_OLD_TABLE
        READ_BOTH_AND_COMPARE
    }

    %% 关系定义
    AbstractReadStrategy ..|> IReadOperationHandler
    ShardingReadStrategy --|> AbstractReadStrategy
    GrayscaleReadTransitionalStrategy ..|> IReadOperationHandler
    GrayscaleReadTransitionalStrategy --> ShardingReadStrategy : decorates
    GrayscaleReadTransitionalStrategy --> IGrayscaleRoutingDecisionMaker : uses
    GrayscaleRoutingDecisionMaker ..|> IGrayscaleRoutingDecisionMaker
```

### 4.8.4 配置管理和策略工厂类图

```mermaid
classDiagram
    %% 配置管理
    class IShardingStrategyConfig {
        <<interface>>
        +isDualWriteEnabled() boolean
        +getReadPathMode() String
        +getGrayscalePercentage() int
        +getQueryLimitConfig() QueryLimitConfig
    }

    class DynamicShardingStrategyConfig {
        <<@Component>>
        -configCenterClient: IConfigCenterClient
        -configChangeListener: IConfigChangeListener
        +isDualWriteEnabled() boolean
        +getReadPathMode() String
        +getGrayscalePercentage() int
        +initConfigListener() void
        -onDualWriteConfigChanged(String, String) void
    }

    %% 策略工厂
    class IStrategyFactory {
        <<interface>>
        +createWriteStrategy() IWriteOperationHandler
        +createReadStrategy() IReadOperationHandler
    }

    class ConditionalStrategyFactory {
        <<@Component>>
        -strategyConfig: IShardingStrategyConfig
        -applicationContext: ApplicationContext
        +createWriteStrategy() IWriteOperationHandler
        +createReadStrategy() IReadOperationHandler
    }

    %% HintManager操作封装
    class IHintManagerOperations {
        <<interface>>
        +setDatabaseShardingValue(String, String) AutoCloseable
        +setTableShardingValue(String, String) AutoCloseable
        +setWriteRouteOnly() AutoCloseable
        +setReadRouteOnly() AutoCloseable
    }

    class SafeHintManagerOperations {
        <<@Component>>
        +setDatabaseShardingValue(String, String) AutoCloseable
        +setTableShardingValue(String, String) AutoCloseable
        +setWriteRouteOnly() AutoCloseable
        +setReadRouteOnly() AutoCloseable
        -createAutoCloseableHint(HintManager) AutoCloseable
    }

    %% 手动路由策略
    class IManualRoutingStrategy {
        <<interface>>
        +applyRouting(QueryContext) AutoCloseable
    }

    class MigrationManualRoutingStrategy {
        <<@Component>>
        -hintOperations: IHintManagerOperations
        -migrationConfig: IMigrationConfigManager
        +applyRouting(QueryContext) AutoCloseable
        -applyDualWriteRouting(QueryContext) AutoCloseable
        -applyGrayscaleReadRouting(QueryContext) AutoCloseable
    }

    %% 查询限制配置
    class QueryLimitConfig {
        -maxListLimit: int
        -maxPaginationSize: int
        -scanTimeoutSeconds: int
        +getMaxListLimit() int
        +getMaxPaginationSize() int
    }

    %% 关系定义
    DynamicShardingStrategyConfig ..|> IShardingStrategyConfig
    ConditionalStrategyFactory ..|> IStrategyFactory
    ConditionalStrategyFactory --> IShardingStrategyConfig : uses
    SafeHintManagerOperations ..|> IHintManagerOperations
    MigrationManualRoutingStrategy ..|> IManualRoutingStrategy
    MigrationManualRoutingStrategy --> IHintManagerOperations : uses
    DynamicShardingStrategyConfig --> QueryLimitConfig : creates
```

通过这套完整的调度引擎与AOP实现方案，我们实现了：

1. **零侵入的业务集成**：通过AOP切面，业务代码无需任何修改
2. **灵活的策略切换**：通过配置中心动态控制不同阶段的策略
3. **强一致性保障**：通过事务管理确保主表和索引表的数据一致性
4. **高性能的查询路由**：通过智能路由决策实现最优的查询性能
5. **安全的迁移过程**：通过双写和灰度策略确保平滑的数据迁移

整个方案在保证系统稳定性的同时，为分库分表的复杂场景提供了完整的解决方案。
```
```
